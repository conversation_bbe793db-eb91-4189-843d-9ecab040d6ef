package com.tqhit.battery.one.features.stats.discharge.presentation

import android.content.Context
import android.os.Bundle
import android.os.PowerManager
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater
import com.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper
import com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.CancellationException
import javax.inject.Inject

/**
 * The main fragment for the discharge screen
 */
@AndroidEntryPoint
class DischargeFragment : Fragment() {
    companion object {
        private const val TAG = "DischargeFragment"
    }

    private val viewModel: DischargeViewModel by viewModels()
    private var _binding: NewFragmentDischargeBinding? = null
    private val binding get() = _binding!!
    
    // Helper classes - lazy initialization for better memory usage
    private val uiUpdater: DischargeUiUpdater by lazy {
        DischargeUiUpdater(requireContext(), binding, timeConverter)
    }
    private val animationHelper: AnimationHelper by lazy {
        AnimationHelper(binding, uiUpdater)
    }
    @Inject lateinit var infoButtonManager: InfoButtonManager
    
    // Services
    private lateinit var powerManager: PowerManager

    @Inject lateinit var timeConverter: TimeConverter
    @Inject lateinit var appLifecycleManager: AppLifecycleManager

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onCreateView() started at $startTime")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onCreateView called")

        _binding = NewFragmentDischargeBinding.inflate(inflater, container, false)

        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onCreateView() completed in ${System.currentTimeMillis() - startTime}ms")
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onViewCreated called")

        val powerManagerStartTime = System.currentTimeMillis()
        powerManager = requireContext().getSystemService(Context.POWER_SERVICE) as PowerManager
        Log.d(TAG, "STARTUP_TIMING: PowerManager initialization took ${System.currentTimeMillis() - powerManagerStartTime}ms")

        // Initialize critical components immediately
        val observeStartTime = System.currentTimeMillis()
        observeUiState()
        Log.d(TAG, "STARTUP_TIMING: observeUiState() took ${System.currentTimeMillis() - observeStartTime}ms")

        // Defer non-critical initialization to improve startup performance
        initializeNonCriticalComponentsAsync()

        Log.d(TAG, "STARTUP_TIMING: DischargeFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onResume called - Fragment is now ACTIVE")
        appLifecycleManager.setDischargeFragmentActive(true)

        // Notify UI updater about resume
        uiUpdater.onFragmentResumed()

        // Trigger UI validation on resume to catch any staleness issues
        viewLifecycleOwner.lifecycleScope.launch {
            delay(500) // Small delay to ensure UI is ready
            val currentState = viewModel.uiState.value
            if (_binding != null && !currentState.isLoadingInitial) {
                Log.d(TAG, "FRAGMENT_LIFECYCLE: Validating UI state on resume")
                Log.d(TAG, "FRAGMENT_LIFECYCLE: ${uiUpdater.getStalenessMetrics()}")
                uiUpdater.updateStatusAndEstimates(currentState)
                uiUpdater.updateLossOfCharge(currentState)
                uiUpdater.updateCurrentSessionDetails(currentState)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onPause called - Fragment is now INACTIVE")
        appLifecycleManager.setDischargeFragmentActive(false)

        // Notify UI updater about pause for background tracking
        uiUpdater.onFragmentPaused()
    }

    override fun onDestroyView() {
        Log.d(TAG, "FRAGMENT_LIFECYCLE: onDestroyView called")
        appLifecycleManager.setDischargeFragmentActive(false)
        super.onDestroyView()
        animationHelper.cleanup()
        _binding = null
    }

    private fun initializeHelpers() {
        // Trigger lazy initialization by accessing the properties
        // This ensures they are created when needed
        uiUpdater.toString() // Trigger lazy init
        animationHelper.toString() // Trigger lazy init

        // Initialize InfoButtonManager with binding
        infoButtonManager.initialize(binding)
    }

    /**
     * Initialize non-critical components asynchronously to improve startup performance
     */
    private fun initializeNonCriticalComponentsAsync() {
        // Use viewLifecycleOwner to ensure proper lifecycle management
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            try {
                // Check if fragment is still attached before proceeding
                if (!isAdded || _binding == null) {
                    Log.d(TAG, "Fragment not attached, skipping async initialization")
                    return@launch
                }

                // Initialize helpers in background
                val helpersStartTime = System.currentTimeMillis()
                withContext(Dispatchers.Default) {
                    // Prepare helper data in background if needed
                }

                // Check again before UI operations
                if (!isAdded || _binding == null) {
                    Log.d(TAG, "Fragment detached during async initialization")
                    return@launch
                }

                // Initialize helpers on main thread (they need UI access)
                initializeHelpers()
                Log.d(TAG, "STARTUP_TIMING: Async initializeHelpers() took ${System.currentTimeMillis() - helpersStartTime}ms")

                // Set up info button listeners
                val listenersStartTime = System.currentTimeMillis()
                infoButtonManager.setupInfoButtonListeners(
                    resetSessionCallback = { resetSession() },
                    getCurrentSession = { viewModel.uiState.value.currentSession },
                    getBatteryCapacity = { viewModel.uiState.value.batteryCapacityMah }
                )
                Log.d(TAG, "STARTUP_TIMING: Async setupInfoButtonListeners() took ${System.currentTimeMillis() - listenersStartTime}ms")

            } catch (e: Exception) {
                // Only log if it's not a cancellation exception
                if (e !is kotlinx.coroutines.CancellationException) {
                    Log.e(TAG, "Error in async initialization", e)
                }

                // Fallback to synchronous initialization only if fragment is still attached
                if (isAdded && _binding != null) {
                    try {
                        initializeHelpers()
                        infoButtonManager.setupInfoButtonListeners(
                            resetSessionCallback = { resetSession() },
                            getCurrentSession = { viewModel.uiState.value.currentSession },
                            getBatteryCapacity = { viewModel.uiState.value.batteryCapacityMah }
                        )
                    } catch (fallbackException: Exception) {
                        Log.e(TAG, "Error in fallback initialization", fallbackException)
                    }
                }
            }
        }
    }
    
    private fun observeUiState() {
        // Primary UI updates - tied to fragment lifecycle for normal operation
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { state ->
                    Log.d(TAG, "VM_STATE: Update from ViewModel: percentage=${state.batteryPercentage}, loading=${state.isLoadingInitial}, charging=${state.isCharging}")

                    // Ensure binding is valid before UI updates
                    if (!ensureBindingValid()) {
                        Log.w(TAG, "VM_STATE: Skipping UI update - binding not valid")
                        return@collect
                    }

                    // AnimationHelper will call uiUpdater.updateStatusAndEstimates
                    animationHelper.animateBatteryUpdate(state)

                    // Update other UI sections that depend on the full state
                    uiUpdater.updateLossOfCharge(state)
                    uiUpdater.updateCurrentSessionDetails(state)
                }
            }
        }

        // Background UI updates - independent of fragment lifecycle for app resume scenarios
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                appLifecycleManager.appState.collect { appState ->
                    Log.d(TAG, "APP_STATE: App state changed to $appState")

                    // Trigger UI update when app comes to foreground and fragment is inactive
                    if (appLifecycleManager.shouldTriggerUiUpdate()) {
                        Log.i(TAG, "BACKGROUND_UPDATE: Triggering UI update due to app state change")

                        // Get current state and update UI
                        val currentState = viewModel.uiState.value
                        if (_binding != null) {
                            uiUpdater.updateLossOfCharge(currentState)
                            uiUpdater.updateCurrentSessionDetails(currentState)
                        }
                    }
                }
            }
        }
    }

    /**
     * Ensures view binding is valid and ready for UI updates
     * This helps prevent UI staleness issues
     */
    private fun ensureBindingValid(): Boolean {
        if (_binding == null) {
            Log.w(TAG, "BINDING_CHECK: View binding is null")
            return false
        }

        if (!isAdded) {
            Log.w(TAG, "BINDING_CHECK: Fragment is not added to activity")
            return false
        }

        if (view == null) {
            Log.w(TAG, "BINDING_CHECK: Fragment view is null")
            return false
        }

        // Check if key UI elements are accessible
        try {
            val percentageView = binding.includeStatusAndEstimates.saeTvPercentage
            if (percentageView.parent == null) {
                Log.w(TAG, "BINDING_CHECK: Percentage view is not attached to parent")
                return false
            }
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_CHECK: Error accessing UI elements", e)
            return false
        }

        return true
    }

    fun resetSession() {
        Log.d(TAG, "resetSession() called by UI - Triggering ViewModel to reset session data.")
        viewModel.resetSessionData()
    }
}
